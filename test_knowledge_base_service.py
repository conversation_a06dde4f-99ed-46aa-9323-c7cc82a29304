#!/usr/bin/env python3
"""
Test script for KnowledgeBaseService methods.
Tests the new retrieve_sentences_by_ids and format_source_nodes_to_grouped_sources methods.
"""

import asyncio
import json
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

async def test_retrieve_sentences_by_ids():
    """Test the retrieve_sentences_by_ids method with mock data."""
    print("Testing retrieve_sentences_by_ids method...")
    
    # Mock sent_ids for testing
    test_sent_ids = [
        "test-sent-id-1",
        "test-sent-id-2", 
        "test-sent-id-3"
    ]
    
    print(f"Test sent_ids: {test_sent_ids}")
    
    # Expected output format
    expected_format = [
        {
            "source": "document1.pdf",
            "sentences": [
                {
                    "sent_id": "test-sent-id-1",
                    "text": "This is a test sentence.",
                    "page_number": 1
                }
            ]
        }
    ]
    
    print("Expected format:")
    print(json.dumps(expected_format, indent=2, ensure_ascii=False))
    
    print("\nNote: This is a structure test. To run with real data, you need:")
    print("1. A valid UserTenantDB instance")
    print("2. Qdrant connection with legal_sentence collection")
    print("3. Actual sent_ids in the collection")

async def test_format_source_nodes_to_grouped_sources():
    """Test the format_source_nodes_to_grouped_sources method with mock data."""
    print("\nTesting format_source_nodes_to_grouped_sources method...")
    
    # Mock source nodes data
    mock_source_nodes = [
        {
            "id": "node-1",
            "text": "Sample text from document 1",
            "score": 0.85,
            "metadata": {
                "source": "legal_document_1.pdf",
                "page_number": 1,
                "sent_id": "sent-1"
            }
        },
        {
            "id": "node-2", 
            "text": "Another text from document 1",
            "score": 0.78,
            "metadata": {
                "source": "legal_document_1.pdf",
                "page_number": 2,
                "sent_id": "sent-2"
            }
        },
        {
            "id": "node-3",
            "text": "Text from different document",
            "score": 0.72,
            "metadata": {
                "source": "legal_document_2.pdf", 
                "page_number": 1,
                "sent_id": "sent-3"
            }
        }
    ]
    
    print("Mock source nodes:")
    print(json.dumps(mock_source_nodes, indent=2, ensure_ascii=False))
    
    # Expected grouped output
    expected_grouped = [
        {
            "source": "legal_document_1.pdf",
            "sentences": [
                {
                    "sent_id": "sent-1",
                    "text": "Full sentence text from Qdrant",
                    "page_number": 1,
                    "score": 0.85
                },
                {
                    "sent_id": "sent-2", 
                    "text": "Full sentence text from Qdrant",
                    "page_number": 2,
                    "score": 0.78
                }
            ]
        },
        {
            "source": "legal_document_2.pdf",
            "sentences": [
                {
                    "sent_id": "sent-3",
                    "text": "Full sentence text from Qdrant", 
                    "page_number": 1,
                    "score": 0.72
                }
            ]
        }
    ]
    
    print("\nExpected grouped format:")
    print(json.dumps(expected_grouped, indent=2, ensure_ascii=False))

def test_method_signatures():
    """Test that the method signatures are correct."""
    print("\nTesting method signatures...")
    
    from app.v1.services.knowledge_base.service import KnowledgeBaseService
    
    # Check if methods exist
    assert hasattr(KnowledgeBaseService, 'retrieve_sentences_by_ids'), "retrieve_sentences_by_ids method not found"
    assert hasattr(KnowledgeBaseService, 'format_source_nodes_to_grouped_sources'), "format_source_nodes_to_grouped_sources method not found"
    assert hasattr(KnowledgeBaseService, 'search_documents_grouped'), "search_documents_grouped method not found"
    
    print("✓ All required methods exist")
    
    # Check method signatures using inspect
    import inspect
    
    # Check retrieve_sentences_by_ids signature
    sig = inspect.signature(KnowledgeBaseService.retrieve_sentences_by_ids)
    params = list(sig.parameters.keys())
    expected_params = ['self', 'sent_ids', 'current_user']
    assert params == expected_params, f"retrieve_sentences_by_ids params: {params}, expected: {expected_params}"
    print("✓ retrieve_sentences_by_ids signature correct")
    
    # Check format_source_nodes_to_grouped_sources signature  
    sig = inspect.signature(KnowledgeBaseService.format_source_nodes_to_grouped_sources)
    params = list(sig.parameters.keys())
    expected_params = ['self', 'source_nodes', 'current_user']
    assert params == expected_params, f"format_source_nodes_to_grouped_sources params: {params}, expected: {expected_params}"
    print("✓ format_source_nodes_to_grouped_sources signature correct")
    
    # Check search_documents_grouped signature
    sig = inspect.signature(KnowledgeBaseService.search_documents_grouped)
    params = list(sig.parameters.keys())
    expected_params = ['query', 'current_user', 'max_results']
    assert params == expected_params, f"search_documents_grouped params: {params}, expected: {expected_params}"
    print("✓ search_documents_grouped signature correct")

async def main():
    """Run all tests."""
    print("=" * 60)
    print("KnowledgeBaseService Implementation Tests")
    print("=" * 60)
    
    try:
        # Test method signatures
        test_method_signatures()
        
        # Test with mock data
        await test_retrieve_sentences_by_ids()
        await test_format_source_nodes_to_grouped_sources()
        
        print("\n" + "=" * 60)
        print("✓ All tests passed! Implementation looks correct.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
