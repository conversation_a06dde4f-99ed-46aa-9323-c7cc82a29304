# app/v1/services/agents/orchestrator.py

import time
import re
from typing import Optional, Dict, Any
from langchain_openai import Chat<PERSON>penAI
from langchain_core.messages import HumanMessage, SystemMessage

from app.shared.database.models import UserTenantDB
from app.shared.config.agent_config import agent_config
from .base_agent import BaseAgent, AgentError
from .types import AgentR<PERSON>po<PERSON>, AgentContext, AgentType, AgentStatus
from .registry import agent_registry

class OrchestratorAgent(BaseAgent):
    """
    Main orchestrator agent that routes user queries to appropriate sub-agents.
    
    The orchestrator:
    1. Analyzes incoming user queries
    2. Determines the best agent to handle the query
    3. Routes the query to the selected agent
    4. Handles fallback responses when no specific agent is suitable
    5. Manages conversation flow and context
    
    Routing Logic:
    - Document search queries → DocumentSearchAgent
    - General legal questions → GeneralChatAgent (if available)
    - Fallback → Direct response from orchestrator
    """
    
    def __init__(self):
        super().__init__(
            name="orchestrator",
            agent_type=AgentType.ORCHESTRATOR,
            timeout_seconds=agent_config.AGENT_DEFAULT_TIMEOUT + 10,  # Extra time for routing
            max_retries=agent_config.AGENT_MAX_RETRIES
        )
        
        # Initialize LLM for intent classification
        self.llm = ChatOpenAI(
            model=agent_config.AGENT_LLM_MODEL,
            temperature=agent_config.AGENT_LLM_TEMPERATURE,
            max_tokens=200  # Small token limit for intent classification
        )
        
        # Define routing keywords and patterns
        self.document_search_keywords = [
            # Nepali keywords
            "खोज", "फेला", "दस्तावेज", "कानून", "ऐन", "नियम", "धारा", "उपधारा",
            "मुद्दा", "फैसला", "अदालत", "न्यायालय", "संविधान", "कानुनी",
            # English keywords
            "search", "find", "document", "law", "act", "rule", "section",
            "case", "judgment", "court", "constitution", "legal"
        ]
        
        self.question_patterns = [
            # Nepali question patterns
            r"के\s+हो", r"कसरी", r"किन", r"कहाँ", r"कहिले", r"को\s+हो",
            r"\?\s*$", r"भन्नुहोस्", r"बताउनुहोस्", r"व्याख्या",
            # English question patterns  
            r"what\s+is", r"how\s+to", r"why", r"where", r"when", r"who\s+is",
            r"\?\s*$", r"explain", r"tell\s+me", r"describe"
        ]
    
    async def _process_query(self, context: AgentContext) -> AgentResponse:
        """
        Process a query by routing it to the appropriate agent.
        
        Args:
            context: The context containing user query and metadata
            
        Returns:
            AgentResponse: Response from the selected agent or fallback response
        """
        start_time = time.time()
        
        try:
            # Determine the best agent for this query
            selected_agent_name = await self._route_query(context)
            
            if selected_agent_name:
                # Route to selected agent
                selected_agent = agent_registry.get_agent(selected_agent_name)
                if selected_agent:
                    self.logger.info(f"Routing query to agent: {selected_agent_name}")
                    
                    # Create a mock user for the sub-agent
                    mock_user = UserTenantDB(
                        id=context.user_id,
                        tenant_id=context.tenant_id,
                        username="orchestrator_user",
                        email="<EMAIL>",
                        role="user"
                    )
                    
                    # Execute the selected agent
                    response = await selected_agent.run(context, mock_user)
                    
                    # Add orchestrator metadata
                    response.metadata.update({
                        "routed_to": selected_agent_name,
                        "orchestrator_processing_time_ms": (time.time() - start_time) * 1000
                    })
                    
                    return response
                else:
                    self.logger.warning(f"Selected agent {selected_agent_name} not found in registry")
            
            # Fallback: Handle query directly
            return await self._handle_fallback(context, start_time)
            
        except Exception as e:
            self.logger.error(f"Error in orchestrator: {e}")
            
            processing_time = (time.time() - start_time) * 1000
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )
    
    async def _route_query(self, context: AgentContext) -> Optional[str]:
        """
        Determine which agent should handle the query.

        Args:
            context: The context containing user query and metadata

        Returns:
            Name of the selected agent or None for fallback
        """
        query = context.user_query.lower()

        try:
            # Rule-based routing first (faster)

            # Check for document search keywords
            matched_keywords = [kw for kw in self.document_search_keywords if kw in query]
            if matched_keywords:
                self.logger.debug(f"Matched document search keywords: {matched_keywords}")
                doc_agents = agent_registry.get_agents_by_type(AgentType.DOCUMENT_SEARCH)
                if doc_agents:
                    self.logger.info(f"Routing to document search agent: {doc_agents[0].name}")
                    return doc_agents[0].name

            # Check for question patterns that might need document search
            matched_patterns = [p for p in self.question_patterns if re.search(p, query, re.IGNORECASE)]
            if matched_patterns:
                self.logger.debug(f"Matched question patterns: {matched_patterns}")
                # Use LLM for more sophisticated intent classification
                intent = await self._classify_intent(context.user_query)
                self.logger.debug(f"LLM classified intent as: {intent}")

                if intent == "document_search":
                    doc_agents = agent_registry.get_agents_by_type(AgentType.DOCUMENT_SEARCH)
                    if doc_agents:
                        self.logger.info(f"Routing to document search agent via LLM: {doc_agents[0].name}")
                        return doc_agents[0].name
                elif intent == "general_chat":
                    chat_agents = agent_registry.get_agents_by_type(AgentType.GENERAL_CHAT)
                    if chat_agents:
                        self.logger.info(f"Routing to general chat agent: {chat_agents[0].name}")
                        return chat_agents[0].name

            # Default to document search for legal queries
            legal_words = [word for word in ["कानून", "ऐन", "नियम", "law", "legal", "act"] if word in query]
            if legal_words:
                self.logger.debug(f"Matched legal words: {legal_words}")
                doc_agents = agent_registry.get_agents_by_type(AgentType.DOCUMENT_SEARCH)
                if doc_agents:
                    self.logger.info(f"Routing to document search agent via legal words: {doc_agents[0].name}")
                    return doc_agents[0].name

            # For any query that looks like it needs information, route to document search
            if any(re.search(p, query, re.IGNORECASE) for p in [r"when", r"कहिले", r"what", r"के"]):
                self.logger.debug("Query appears to be asking for information, routing to document search")
                doc_agents = agent_registry.get_agents_by_type(AgentType.DOCUMENT_SEARCH)
                if doc_agents:
                    self.logger.info(f"Routing to document search agent for information query: {doc_agents[0].name}")
                    return doc_agents[0].name

            self.logger.info("No specific agent matched, using fallback")
            return None

        except Exception as e:
            self.logger.error(f"Error in query routing: {e}")
            # Fallback to document search if routing fails
            doc_agents = agent_registry.get_agents_by_type(AgentType.DOCUMENT_SEARCH)
            if doc_agents:
                self.logger.info(f"Error fallback to document search: {doc_agents[0].name}")
                return doc_agents[0].name
            return None
    
    async def _classify_intent(self, query: str) -> str:
        """
        Use LLM to classify the intent of the query.
        
        Args:
            query: The user query
            
        Returns:
            Intent classification: "document_search", "general_chat", or "unknown"
        """
        try:
            system_prompt = """You are an intent classifier for a legal assistant system. 
            Classify the user query into one of these categories:
            - "document_search": User wants to search for specific legal documents, laws, cases, or regulations
            - "general_chat": User has general legal questions or wants explanations
            - "unknown": Query doesn't fit the above categories
            
            Respond with only the category name."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Classify this query: {query}")
            ]
            
            response = await self.llm.ainvoke(messages)
            intent = response.content.strip().lower()
            
            if intent in ["document_search", "general_chat", "unknown"]:
                return intent
            else:
                return "unknown"
                
        except Exception as e:
            self.logger.error(f"Error in intent classification: {e}")
            return "unknown"
    
    async def _handle_fallback(self, context: AgentContext, start_time: float) -> AgentResponse:
        """
        Handle queries that don't match any specific agent.
        
        Args:
            context: The context containing user query and metadata
            start_time: Start time for processing time calculation
            
        Returns:
            AgentResponse: Fallback response from orchestrator
        """
        try:
            # Use LLM to generate a helpful response
            system_prompt = agent_config.ORCHESTRATOR_SYSTEM_PROMPT
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=context.user_query)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            processing_time = (time.time() - start_time) * 1000
            
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.SUCCESS,
                content=response.content,
                metadata={
                    "fallback_response": True,
                    "available_agents": agent_registry.list_agents()
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Error in fallback handling: {e}")
            
            processing_time = (time.time() - start_time) * 1000
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, म तपाईंको प्रश्नको जवाफ दिन सक्दिन। कृपया अझ स्पष्ट प्रश्न सोध्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )
    
    def get_routing_info(self) -> Dict[str, Any]:
        """Get information about routing capabilities."""
        return {
            "available_agents": agent_registry.list_agents(),
            "routing_keywords": {
                "document_search": self.document_search_keywords[:5],  # Show first 5
                "question_patterns": self.question_patterns[:3]  # Show first 3
            },
            "fallback_enabled": True,
            "llm_classification": True
        }
