# app/v1/services/agents/document_search_agent.py

import time
from typing import List
from app.shared.database.models import UserTenantDB
from app.v1.services.knowledge_base.service import KnowledgeBaseService
from app.v1.services.chat.service import ChatService
from app.shared.config.agent_config import agent_config
from .base_agent import BaseAgent, Agent<PERSON>rror
from .types import Agent<PERSON><PERSON>po<PERSON>, AgentContext, AgentType, AgentStatus, SourceReference

class DocumentSearchAgent(BaseAgent):
    """
    Agent specialized in searching legal documents and retrieving relevant information.
    
    This agent wraps the existing KnowledgeBaseService and ChatService functionality
    to provide document search capabilities within the multi-agent framework.
    
    Capabilities:
    - Vector search using embeddings
    - Text-based fallback search
    - Nepali text processing
    - Source document references
    - AI-powered response generation
    """
    
    def __init__(self):
        super().__init__(
            name="document_search",
            agent_type=AgentType.DOCUMENT_SEARCH,
            timeout_seconds=agent_config.AGENT_DEFAULT_TIMEOUT,
            max_retries=agent_config.AGENT_MAX_RETRIES
        )
        self.max_results = agent_config.DOC_SEARCH_AGENT_MAX_RESULTS
        self.include_sources = agent_config.DOC_SEARCH_AGENT_INCLUDE_SOURCES
    
    async def _process_query(self, context: AgentContext) -> AgentResponse:
        """
        Process a document search query using the existing knowledge base services.
        
        Args:
            context: The context containing user query and metadata
            
        Returns:
            AgentResponse: Response containing search results and AI-generated answer
        """
        start_time = time.time()
        
        try:
            # Create a mock user for the existing services
            # In production, this should be passed from the orchestrator
            mock_user = UserTenantDB(
                id=context.user_id,
                tenant_id=context.tenant_id,
                username="agent_user",
                email="<EMAIL>",
                role="user"
            )
            
            # Use the existing ChatService to process the query
            # This provides both search and AI response generation
            chat_response = await ChatService.process_chat_query(
                query=context.user_query,
                current_user=mock_user,
                max_results=min(context.max_results, self.max_results),
                conversation_id=context.conversation_id,
                include_sources=context.include_sources and self.include_sources
            )
            
            # Convert ChatService response to AgentResponse format with uniform sources
            legacy_sources = []
            if chat_response.sources:
                for source in chat_response.sources:
                    legacy_sources.append(SourceReference(
                        document_id=source.document_id,
                        filename=source.filename,
                        page_number=source.page_number,
                        chunk_id=source.chunk_id,
                        text_snippet=source.text_snippet,
                        confidence_score=source.confidence_score,
                        minio_path=source.minio_path,
                        presigned_url=source.presigned_url,
                        coordinates=source.coordinates
                    ))

            processing_time = (time.time() - start_time) * 1000

            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.SUCCESS,
                content=chat_response.answer,
                uniform_sources=chat_response.uniform_sources,  # Use uniform format from chat response
                sources=legacy_sources,  # Legacy format for backward compatibility
                metadata={
                    "total_sources_found": chat_response.total_sources_found,
                    "language": chat_response.language,
                    "conversation_id": chat_response.conversation_id,
                    "original_processing_time_ms": chat_response.processing_time_ms
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Error in document search agent: {e}")

            # Try fallback search if the main search fails
            try:
                self.logger.info("Attempting fallback search...")
                return await self._fallback_search(context, start_time)
            except Exception as fallback_error:
                self.logger.error(f"Fallback search also failed: {fallback_error}")

                processing_time = (time.time() - start_time) * 1000

                # Provide specific error messages
                if "database" in str(e).lower() or "connection" in str(e).lower():
                    error_content = "माफ गर्नुहोस्, डाटाबेस जडान समस्या भयो। कृपया केही समयपछि प्रयास गर्नुहोस्।"
                elif "timeout" in str(e).lower():
                    error_content = "माफ गर्नुहोस्, खोज गर्न धेरै समय लाग्यो। कृपया छोटो प्रश्न सोध्नुहोस्।"
                else:
                    error_content = "माफ गर्नुहोस्, दस्तावेज खोज्दा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।"

                return AgentResponse(
                    agent_type=self.agent_type,
                    agent_name=self.name,
                    status=AgentStatus.ERROR,
                    content=error_content,
                    processing_time_ms=processing_time,
                    error_message=str(e),
                    metadata={
                        "fallback_attempted": True,
                        "fallback_error": str(fallback_error)
                    }
                )
    
    async def _fallback_search(self, context: AgentContext, start_time: float) -> AgentResponse:
        """
        Fallback search using only the knowledge base service without AI response.
        
        Args:
            context: The context containing user query and metadata
            start_time: Start time for processing time calculation
            
        Returns:
            AgentResponse: Response with search results only
        """
        try:
            # Create a mock user for the existing services
            mock_user = UserTenantDB(
                id=context.user_id,
                tenant_id=context.tenant_id,
                username="agent_user",
                email="<EMAIL>",
                role="user"
            )
            
            # Use KnowledgeBaseService directly for search
            kb_service = KnowledgeBaseService(mock_user)
            
            # Use vector search to get grouped results
            search_results = await kb_service.search_documents(
                context.user_query,
                mock_user,
                min(context.max_results, self.max_results)
            )
            
            # Convert grouped search results to legacy sources format
            sources = []
            for source_group in search_results:
                source_name = source_group.get("source", "unknown.pdf")
                for sentence in source_group.get("sentences", []):
                    sources.append(SourceReference(
                        document_id=sentence.get("sent_id", ""),
                        filename=source_name,
                        page_number=sentence.get("page_number", 0),
                        chunk_id=sentence.get("sent_id", ""),
                        text_snippet=sentence.get("text", "")[:200] + "..." if len(sentence.get("text", "")) > 200 else sentence.get("text", ""),
                        confidence_score=sentence.get("score", 0.0)
                    ))
            
            processing_time = (time.time() - start_time) * 1000
            
            # Generate a simple response based on search results
            if sources:
                content = f"तपाईंको खोज \"{context.user_query}\" को लागि {len(sources)} वटा सम्बन्धित दस्तावेजहरू फेला परेका छन्।"
            else:
                content = f"माफ गर्नुहोस्, तपाईंको खोज \"{context.user_query}\" को लागि कुनै सम्बन्धित दस्तावेज फेला परेन।"
            
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.PARTIAL if sources else AgentStatus.SUCCESS,
                content=content,
                sources=sources,
                metadata={
                    "total_sources_found": len(sources),
                    "search_type": "fallback",
                    "language": context.language
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Fallback search failed: {e}")
            raise AgentError(f"Both primary and fallback search failed: {e}", self.name, "search")
    
    def get_capabilities(self) -> List[str]:
        """Get list of capabilities this agent provides."""
        return [
            "document_search",
            "vector_search", 
            "text_search",
            "nepali_text_processing",
            "source_references",
            "ai_response_generation"
        ]
